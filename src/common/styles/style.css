@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@200;300;400;500;600;700&family=Quicksand:wght@200;300;400;500;600;700&family=Oswald:wght@200;300;400;500;600;700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@font-face {
  font-family: 'Marlin Soft';
  src: url('/fonts/MarlinSoft.ttf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --black: #2c251b;
  --dark: #12151c;
  --darker: #191d26;
  --primary: #661b1c;
  --primaryDark: #531516;
  --white: #f7f7f7;
}

/* global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
}

body {
  background-color: #121212;
  color: var(--white);
}

/* animation move form left to right */
@keyframes move {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

/* animation for navbar to slide down */
@keyframes slide-down {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(50%);
  }
  100% {
    transform: translateY(0);
  }
}

.Mui-selected {
  color: #fff !important;
}

.MuiButtonBase-root.MuiTab-root.MuiTab-textColorPrimary {
  color: #bfbfbf;
}

.MuiBox-root {
  padding: 0 !important;
}

@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

#canvas{
  width: 100% !important;
}

#wheel{
  /* height: 800px; */
  width: 800px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
/* @media screen and (max-width: 920px) {
  #wheel {
    width: 750px;
    /* justify-content: flex-start; */
  /* }
} */ 
@media screen and (max-width: 800px) {
  #wheel {
    width: 100%;
  }
}